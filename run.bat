@echo off
echo =================================
echo 情绪分析Demo - 运行脚本
echo =================================

if not exist "target\tongyi-asr-demo-1.0.0.jar" (
    echo 错误：找不到编译后的JAR文件！
    echo 请先运行 compile.bat 进行编译
    pause
    exit /b 1
)

echo 开始运行程序...
echo.

java -jar target\tongyi-asr-demo-1.0.0.jar

echo.
echo =================================
echo 程序执行完成
echo =================================
pause
