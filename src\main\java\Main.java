import com.alibaba.dashscope.audio.asr.transcription.*;
import com.google.gson.*;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 解析器，用于解析语音识别结果中的情绪和事件标签
 */
class SenseVoiceParser {

    private static final List<String> EMOTION_LIST = Arrays.asList("NEUTRAL", "HAPPY", "ANGRY", "SAD");
    private static final List<String> EVENT_LIST = Arrays.asList("Speech", "Applause", "BGM", "Laughter");
    private static final List<String> ALL_TAGS = Arrays.asList(
            "Speech", "Applause", "BGM", "Laughter", "NEUTRAL", "HAPPY", "ANGRY", "SAD", "SPECIAL_TOKEN_1");

    /**
     * 解析识别结果
     * @param data JSON格式的转写结果
     * @param keepTrans 是否保留转写文本
     * @param keepEmotions 是否保留情感标签
     * @param keepEvents 是否保留事件标签
     * @return 解析后的结果
     */
    public static JsonObject parseSenseVoiceResult(JsonObject data, boolean keepTrans, boolean keepEmotions, boolean keepEvents) {

        List<String> tagsToCleanup = ALL_TAGS.stream()
                .flatMap(tag -> Stream.of("<|" + tag + "|> ", "<|/" + tag + "|>", "<|" + tag + "|>"))
                .collect(Collectors.toList());

        JsonArray transcripts = data.getAsJsonArray("transcripts");

        for (JsonElement transcriptElement : transcripts) {
            JsonObject transcript = transcriptElement.getAsJsonObject();
            JsonArray sentences = transcript.getAsJsonArray("sentences");

            for (JsonElement sentenceElement : sentences) {
                JsonObject sentence = sentenceElement.getAsJsonObject();
                String text = sentence.get("text").getAsString();

                if (keepEmotions) {
                    extractTags(sentence, text, EMOTION_LIST, "emotion");
                }

                if (keepEvents) {
                    extractTags(sentence, text, EVENT_LIST, "event");
                }

                if (keepTrans) {
                    String cleanText = getCleanText(text, tagsToCleanup);
                    sentence.addProperty("text", cleanText);
                } else {
                    sentence.remove("text");
                }
            }

            if (keepTrans) {
                transcript.addProperty("text", getCleanText(transcript.get("text").getAsString(), tagsToCleanup));
            } else {
                transcript.remove("text");
            }

            JsonArray filteredSentences = new JsonArray();
            for (JsonElement sentenceElement : sentences) {
                JsonObject sentence = sentenceElement.getAsJsonObject();
                if (sentence.has("text") || sentence.has("emotion") || sentence.has("event")) {
                    filteredSentences.add(sentence);
                }
            }
            transcript.add("sentences", filteredSentences);
        }
        return data;
    }

    private static void extractTags(JsonObject sentence, String text, List<String> tagList, String key) {
        String pattern = "<\\|(" + String.join("|", tagList) + ")\\|>";
        Pattern compiledPattern = Pattern.compile(pattern);
        Matcher matcher = compiledPattern.matcher(text);
        Set<String> tags = new HashSet<>();

        while (matcher.find()) {
            tags.add(matcher.group(1));
        }

        if (!tags.isEmpty()) {
            JsonArray tagArray = new JsonArray();
            tags.forEach(tagArray::add);
            sentence.add(key, tagArray);
        } else {
            sentence.remove(key);
        }
    }

    private static String getCleanText(String text, List<String> tagsToCleanup) {
        for (String tag : tagsToCleanup) {
            text = text.replace(tag, "");
        }
        return text.replaceAll("\\s{2,}", " ").trim();
    }
}

/**
 * 情绪分析Demo
 * 使用模型进行语音识别和情绪分析
 */
public class Main {
    
    // API Key - 请替换为您的实际API Key
    private static final String API_KEY = "sk-c2ef44dd6ee14584bad5b8d70203b87f";
    
    // 测试音频文件URL
    private static final String TEST_AUDIO_URL = "https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/sensevoice/rich_text_example_1.wav";

    public static void main(String[] args) {
        System.out.println("=== 情绪分析Demo ===");
        System.out.println("使用模型进行语音识别和情绪分析");
        System.out.println("测试音频: " + TEST_AUDIO_URL);
        System.out.println();

        // 配置SSL以支持HTTPS连接
        System.out.println("配置SSL...");
        setupSSL();
        System.out.println("SSL配置完成");

        System.out.println("开始处理...");

        // 测试模式：使用模拟数据
        if (args.length > 0 && "test".equals(args[0])) {
            testWithMockData();
            return;
        }

        try {
            // 创建转写请求参数
            TranscriptionParam param = TranscriptionParam.builder()
                    .apiKey(API_KEY)
                    .model("sensevoice-v1")
                    .fileUrls(Arrays.asList(TEST_AUDIO_URL))
                    .parameter("language_hints", new String[] {"en"})
                    .parameter("channel_id", new int[] {0})
                    // 启用情绪和事件检测的关键参数
                    .parameter("enable_words", true)
                    .parameter("enable_inverse_text_normalization", true)
                    .build();

            Transcription transcription = new Transcription();
            
            // 提交转写请求
            System.out.println("正在提交语音识别任务...");
            System.out.println("使用参数：");
            //System.out.println("- 模型: sensevoice-v1");
            System.out.println("- 语言提示: en");
            System.out.println("- 启用词级别时间戳: true");
            System.out.println("- 启用逆文本标准化: true");
            TranscriptionResult result = transcription.asyncCall(param);
            System.out.println("任务ID: " + result.getTaskId());
            System.out.println("请求ID: " + result.getRequestId());
            
            // 等待转写完成
            System.out.println("等待识别完成...");
            System.out.println("调用wait方法...");
            result = transcription.wait(
                    TranscriptionQueryParam.FromTranscriptionParam(param, result.getTaskId()));
            System.out.println("wait方法返回");
            
            // 获取转写结果
            List<TranscriptionTaskResult> taskResultList = result.getResults();
            if (taskResultList != null && taskResultList.size() > 0) {
                System.out.println("识别完成！正在解析结果...");
                System.out.println();

                // 直接使用API返回的结果，不从URL下载
                Gson gson = new GsonBuilder().setPrettyPrinting().create();

                // 将整个result对象转换为JSON进行分析
                String resultJson = gson.toJson(result);
                JsonObject fullResult = gson.fromJson(resultJson, JsonObject.class);

                System.out.println("=== 完整API返回结果 ===");
                System.out.println(resultJson);
                System.out.println();

                // 从output.results中获取transcription_url
                JsonElement jsonResult = null;
                if (fullResult.has("output") && fullResult.get("output").isJsonObject()) {
                    JsonObject output = fullResult.get("output").getAsJsonObject();
                    if (output.has("results") && output.get("results").isJsonArray()) {
                        JsonArray resultsArray = output.get("results").getAsJsonArray();
                        if (resultsArray.size() > 0) {
                            JsonObject firstResult = resultsArray.get(0).getAsJsonObject();
                            if (firstResult.has("transcription_url")) {
                                System.out.println("=== 从transcription_url获取详细结果 ===");
                                try {
                                    String transcriptionUrl = firstResult.get("transcription_url").getAsString();
                                    // 解码URL中的转义字符
                                    transcriptionUrl = transcriptionUrl.replace("\\u003d", "=").replace("\\u0026", "&");
                                    System.out.println("下载URL: " + transcriptionUrl);

                                    HttpURLConnection connection =
                                            (HttpURLConnection) new URL(transcriptionUrl).openConnection();
                                    connection.setRequestMethod("GET");
                                    connection.connect();

                                    BufferedReader reader =
                                            new BufferedReader(new InputStreamReader(connection.getInputStream()));

                                    // 先读取原始JSON字符串，保留Unicode转义字符
                                    StringBuilder jsonString = new StringBuilder();
                                    String line;
                                    while ((line = reader.readLine()) != null) {
                                        jsonString.append(line);
                                    }
                                    String rawJsonString = jsonString.toString();

                                    // 解析JSON对象
                                    JsonObject jsonObj = gson.fromJson(rawJsonString, JsonObject.class);

                                    // 保存原始JSON字符串供后续使用
                                    jsonObj.addProperty("_raw_json", rawJsonString);
                                    jsonResult = jsonObj;

                                    System.out.println("成功从URL获取转录结果");
                                } catch (Exception e) {
                                    System.out.println("从URL获取结果失败: " + e.getMessage());
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }

                if (jsonResult != null) {
                    System.out.println("=== 解析的转录结果 ===");
                    System.out.println(gson.toJson(jsonResult));
                    System.out.println();
                } else {
                    System.out.println("=== 未找到转录结果，使用完整结果 ===");
                    jsonResult = fullResult;
                }

                // 解析情绪和事件
                if (jsonResult != null && jsonResult.isJsonObject()) {
                    try {
                        JsonObject parsedResult = SenseVoiceParser.parseSenseVoiceResult(
                                jsonResult.getAsJsonObject(), true, true, true);

                        System.out.println("=== 情绪分析结果 ===");
                        displayEmotionAnalysis(parsedResult);
                    } catch (Exception e) {
                        System.out.println("=== 情绪分析结果 ===");
                        //System.out.println("SenseVoiceParser解析失败: " + e.getMessage());
                    }

                    // 详细解析原始结果中的情绪标签
                    System.out.println("\n=== 详细情绪标注解析 ===");
                    parseDetailedEmotions(jsonResult.getAsJsonObject());
                } else {
                    System.out.println("=== 情绪分析结果 ===");
                    System.out.println("无法解析JSON结果");
                }
            } else {
                System.out.println("未获取到识别结果");
            }
            
        } catch (Exception e) {
            System.out.println("发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 使用模拟数据测试解析功能
     */
    private static void testWithMockData() {
        //System.out.println("=== 测试模式：使用模拟数据 ===");

        // 模拟JSON数据（基于之前成功的结果）
        String mockJsonData = "{\n" +
            "  \"file_url\": \"https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/sensevoice/rich_text_example_1.wav\",\n" +
            "  \"properties\": {\n" +
            "    \"audio_format\": \"pcm_s16le\",\n" +
            "    \"channels\": [0],\n" +
            "    \"original_sampling_rate\": 16000,\n" +
            "    \"original_duration_in_milliseconds\": 17645\n" +
            "  },\n" +
            "  \"transcripts\": [\n" +
            "    {\n" +
            "      \"channel_id\": 0,\n" +
            "      \"content_duration_in_milliseconds\": 13240,\n" +
            "      \"text\": \"\\u003c|Speech|\\u003e Senior staff, principal doris jackson, wakefield faculty, and of course, my fellow classmates. \\u003c|/Speech|\\u003e \\u003c|NEUTRAL|\\u003e\\u003c|Applause|\\u003e \\u003c|Speech|\\u003e I am honored \\u003c|/Applause|\\u003e to have been chosen to speak before my classmates, as well as the students across America today. \\u003c|/Speech|\\u003e\",\n" +
            "      \"sentences\": [\n" +
            "        {\n" +
            "          \"begin_time\": 0,\n" +
            "          \"end_time\": 7480,\n" +
            "          \"text\": \"\\u003c|Speech|\\u003e Senior staff, principal doris jackson, wakefield faculty, and of course, my fellow classmates. \\u003c|/Speech|\\u003e \\u003c|NEUTRAL|\\u003e\"\n" +
            "        },\n" +
            "        {\n" +
            "          \"begin_time\": 11880,\n" +
            "          \"end_time\": 17640,\n" +
            "          \"text\": \"\\u003c|Applause|\\u003e \\u003c|Speech|\\u003e I am honored \\u003c|/Applause|\\u003e to have been chosen to speak before my classmates, as well as the students across America today. \\u003c|/Speech|\\u003e\"\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}";

        try {
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            JsonObject jsonResult = gson.fromJson(mockJsonData, JsonObject.class);

            System.out.println("=== 原始识别结果 ===");
            System.out.println(gson.toJson(jsonResult));
            System.out.println();

            System.out.println("=== 详细情绪标注解析 ===");
            parseDetailedEmotions(jsonResult);

        } catch (Exception e) {
            System.out.println("测试模式出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 配置SSL以支持HTTPS连接
     */
    private static void setupSSL() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
            };

            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // 设置主机名验证器
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);

        } catch (Exception e) {
            System.out.println("SSL配置失败: " + e.getMessage());
        }
    }

    /**
     * 详细解析原始结果中的情绪和事件标签
     */
    private static void parseDetailedEmotions(JsonObject jsonResult) {
        try {
            System.out.println("🔍 调试：开始解析JSON结构");
            System.out.println("JSON keys: " + jsonResult.keySet());

            // 尝试从原始JSON字符串中提取包含标签的文本
            String fullTextWithTags = null;
            if (jsonResult.has("_raw_json")) {
                String rawJson = jsonResult.get("_raw_json").getAsString();
                System.out.println("🔍 调试：找到原始JSON字符串");

                // 使用正则表达式提取text字段的原始值
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\"text\"\\s*:\\s*\"([^\"]+)\"");
                java.util.regex.Matcher matcher = pattern.matcher(rawJson);
                if (matcher.find()) {
                    fullTextWithTags = matcher.group(1);
                    System.out.println("🔍 调试：从原始JSON提取的text: [" + fullTextWithTags + "]");
                }
            }

            // 如果没有找到原始JSON，使用解析后的结果
            if (fullTextWithTags == null) {
                JsonArray transcripts = jsonResult.getAsJsonArray("transcripts");
                if (transcripts != null && transcripts.size() > 0) {
                    JsonObject transcript = transcripts.get(0).getAsJsonObject();
                    fullTextWithTags = transcript.get("text").getAsString();
                    System.out.println("🔍 调试：使用解析后的text: [" + fullTextWithTags + "]");
                }
            }

            if (fullTextWithTags != null) {
                System.out.println("📝 完整转录文本分析：");
                System.out.println("原文（含标签）：" + fullTextWithTags);

                // 解码Unicode转义字符
                String decodedText = fullTextWithTags.replace("\\u003c", "<").replace("\\u003e", ">");
                System.out.println("解码后文本：" + decodedText);

                String cleanFullText = decodedText.replaceAll("<\\|[^|]+\\|>", "").trim();
                System.out.println("纯文本：" + cleanFullText);
                System.out.println();

                // 解析情绪标签
                System.out.println("🎭 情绪标签检测：");
                parseEmotionTags(decodedText);

                // 解析事件标签
                System.out.println("\n🎵 音频事件检测：");
                parseEventTags(decodedText);

                // 按句子分析
                System.out.println("\n📖 分句情绪分析：");
                JsonArray transcripts = jsonResult.getAsJsonArray("transcripts");
                if (transcripts != null && transcripts.size() > 0) {
                    JsonObject transcript = transcripts.get(0).getAsJsonObject();
                    JsonArray sentences = transcript.getAsJsonArray("sentences");
                    if (sentences != null) {
                        for (int i = 0; i < sentences.size(); i++) {
                            JsonObject sentence = sentences.get(i).getAsJsonObject();
                            String sentenceText = sentence.get("text").getAsString();

                            // 从原始JSON中提取句子文本（包含标签）
                            String sentenceTextWithTags = sentenceText;
                            if (jsonResult.has("_raw_json")) {
                                String rawJson = jsonResult.get("_raw_json").getAsString();
                                // 提取sentences数组中第i个句子的原始text
                                try {
                                    String pattern = "\"sentences\"\\s*:\\s*\\[([^\\]]+)\\]";
                                    java.util.regex.Pattern sentencesPattern = java.util.regex.Pattern.compile(pattern);
                                    java.util.regex.Matcher sentencesMatcher = sentencesPattern.matcher(rawJson);
                                    if (sentencesMatcher.find()) {
                                        String sentencesArrayStr = sentencesMatcher.group(1);
                                        // 分割句子，找到第i个句子的text字段
                                        String[] sentenceParts = sentencesArrayStr.split("\\},\\{");
                                        if (i < sentenceParts.length) {
                                            String sentencePart = sentenceParts[i];
                                            java.util.regex.Pattern textPattern = java.util.regex.Pattern.compile("\"text\"\\s*:\\s*\"([^\"]+)\"");
                                            java.util.regex.Matcher textMatcher = textPattern.matcher(sentencePart);
                                            if (textMatcher.find()) {
                                                sentenceTextWithTags = textMatcher.group(1);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    System.out.println("🔍 调试：提取句子原始文本失败: " + e.getMessage());
                                }
                            }

                            // 解码Unicode转义字符
                            String decodedSentenceText = sentenceTextWithTags.replace("\\u003c", "<").replace("\\u003e", ">");
                            int beginTime = sentence.get("begin_time").getAsInt();
                            int endTime = sentence.get("end_time").getAsInt();

                            System.out.println(String.format("句子 %d [%d-%dms]:", i + 1, beginTime, endTime));
                            System.out.println("🔍 调试：句子原始文本: [" + sentenceTextWithTags + "]");
                            System.out.println("🔍 调试：句子解码文本: [" + decodedSentenceText + "]");
                            analyzeSentenceEmotion(decodedSentenceText);
                            System.out.println();
                        }
                    }
                }
            } else {
                System.out.println("❌ 未找到转录文本");
            }
        } catch (Exception e) {
            System.out.println("解析详细情绪时出错: " + e.getMessage());
        }
    }

    /**
     * 解析情绪标签
     */
    private static void parseEmotionTags(String text) {
        String[] emotions = {"NEUTRAL", "HAPPY", "ANGRY", "SAD"};
        String[] emotionEmojis = {"😐", "😊", "😠", "😢"};
        String[] emotionDescriptions = {"中性/平静", "开心/愉悦", "愤怒/生气", "悲伤/难过"};

        boolean foundEmotion = false;
        for (int i = 0; i < emotions.length; i++) {
            if (text.contains("<|" + emotions[i] + "|>")) {
                System.out.println(String.format("  %s %s (%s)",
                    emotionEmojis[i], emotions[i], emotionDescriptions[i]));
                foundEmotion = true;
            }
        }

        if (!foundEmotion) {
            System.out.println("  未检测到明确的情绪标签");
        }
    }

    /**
     * 解析事件标签
     */
    private static void parseEventTags(String text) {
        String[] events = {"Speech", "Applause", "BGM", "Laughter"};
        String[] eventEmojis = {"🗣️", "👏", "🎵", "😂"};
        String[] eventDescriptions = {"语音/说话", "掌声/鼓掌", "背景音乐", "笑声"};

        for (int i = 0; i < events.length; i++) {
            if (text.contains("<|" + events[i] + "|>")) {
                System.out.println(String.format("  %s %s (%s)",
                    eventEmojis[i], events[i], eventDescriptions[i]));
            }
        }
    }

    /**
     * 分析单个句子的情绪
     */
    private static void analyzeSentenceEmotion(String sentenceText) {
        // 清理文本，移除标签
        String cleanText = sentenceText.replaceAll("<\\|[^|]+\\|>", "").trim();
        System.out.println("  文本: " + cleanText);

        // 检测情绪标签
        if (sentenceText.contains("<|NEUTRAL|>")) {
            System.out.println("  情绪: 😐 中性 - 语调平稳，没有明显的情绪波动");
        } else if (sentenceText.contains("<|HAPPY|>")) {
            System.out.println("  情绪: 😊 开心 - 语调轻快，表达积极情绪");
        } else if (sentenceText.contains("<|ANGRY|>")) {
            System.out.println("  情绪: 😠 愤怒 - 语调激烈，表达不满或愤怒");
        } else if (sentenceText.contains("<|SAD|>")) {
            System.out.println("  情绪: 😢 悲伤 - 语调低沉，表达悲伤或失落");
        } else {
            System.out.println("  情绪: ❓ 未标注 - 可能是过渡段落或背景声音");
        }

        // 检测事件
        if (sentenceText.contains("<|Speech|>")) {
            System.out.println("  事件: 🗣️ 正在说话");
        }
        if (sentenceText.contains("<|Applause|>")) {
            System.out.println("  事件: 👏 掌声响起");
        }
        if (sentenceText.contains("<|BGM|>")) {
            System.out.println("  事件: 🎵 背景音乐");
        }
        if (sentenceText.contains("<|Laughter|>")) {
            System.out.println("  事件: 😂 笑声");
        }
    }

    /**
     * 显示情绪分析结果
     */
    private static void displayEmotionAnalysis(JsonObject result) {
        JsonArray transcripts = result.getAsJsonArray("transcripts");
        
        for (JsonElement transcriptElement : transcripts) {
            JsonObject transcript = transcriptElement.getAsJsonObject();
            
            System.out.println("音频文件: " + transcript.get("file_url").getAsString());
            System.out.println("总时长: " + transcript.getAsJsonObject("properties")
                    .get("original_duration_in_milliseconds").getAsInt() + "ms");
            
            if (transcript.has("text")) {
                System.out.println("识别文本: " + transcript.get("text").getAsString());
            }
            System.out.println();
            
            JsonArray sentences = transcript.getAsJsonArray("sentences");
            for (int i = 0; i < sentences.size(); i++) {
                JsonObject sentence = sentences.get(i).getAsJsonObject();
                
                System.out.println("片段 " + (i + 1) + ":");
                System.out.println("  时间: " + sentence.get("begin_time").getAsInt() + 
                        "ms - " + sentence.get("end_time").getAsInt() + "ms");
                
                if (sentence.has("text")) {
                    System.out.println("  文本: " + sentence.get("text").getAsString());
                }
                
                if (sentence.has("emotion")) {
                    JsonArray emotions = sentence.getAsJsonArray("emotion");
                    System.out.print("  情绪: ");
                    for (int j = 0; j < emotions.size(); j++) {
                        String emotion = emotions.get(j).getAsString();
                        String emotionChinese = getEmotionChinese(emotion);
                        System.out.print(emotionChinese + "(" + emotion + ")");
                        if (j < emotions.size() - 1) System.out.print(", ");
                    }
                    System.out.println();
                }
                
                if (sentence.has("event")) {
                    JsonArray events = sentence.getAsJsonArray("event");
                    System.out.print("  事件: ");
                    for (int j = 0; j < events.size(); j++) {
                        String event = events.get(j).getAsString();
                        String eventChinese = getEventChinese(event);
                        System.out.print(eventChinese + "(" + event + ")");
                        if (j < events.size() - 1) System.out.print(", ");
                    }
                    System.out.println();
                }
                System.out.println();
            }
        }
    }
    
    /**
     * 获取情绪的中文描述
     */
    private static String getEmotionChinese(String emotion) {
        switch (emotion) {
            case "NEUTRAL": return "中性";
            case "HAPPY": return "高兴";
            case "ANGRY": return "生气";
            case "SAD": return "伤心";
            default: return emotion;
        }
    }
    
    /**
     * 获取事件的中文描述
     */
    private static String getEventChinese(String event) {
        switch (event) {
            case "Speech": return "说话声";
            case "Applause": return "掌声";
            case "BGM": return "背景音乐";
            case "Laughter": return "笑声";
            default: return event;
        }
    }
}
