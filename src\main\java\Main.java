import com.alibaba.dashscope.audio.asr.transcription.*;
import com.google.gson.*;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * SenseVoice解析器，用于解析语音识别结果中的情绪和事件标签
 */
class SenseVoiceParser {

    private static final List<String> EMOTION_LIST = Arrays.asList("NEUTRAL", "HAPPY", "ANGRY", "SAD");
    private static final List<String> EVENT_LIST = Arrays.asList("Speech", "Applause", "BGM", "Laughter");
    private static final List<String> ALL_TAGS = Arrays.asList(
            "Speech", "Applause", "BGM", "Laughter", "NEUTRAL", "HAPPY", "ANGRY", "SAD", "SPECIAL_TOKEN_1");

    /**
     * 解析SenseVoice识别结果
     * @param data JSON格式的SenseVoice转写结果
     * @param keepTrans 是否保留转写文本
     * @param keepEmotions 是否保留情感标签
     * @param keepEvents 是否保留事件标签
     * @return 解析后的结果
     */
    public static JsonObject parseSenseVoiceResult(JsonObject data, boolean keepTrans, boolean keepEmotions, boolean keepEvents) {

        List<String> tagsToCleanup = ALL_TAGS.stream()
                .flatMap(tag -> Stream.of("<|" + tag + "|> ", "<|/" + tag + "|>", "<|" + tag + "|>"))
                .collect(Collectors.toList());

        JsonArray transcripts = data.getAsJsonArray("transcripts");

        for (JsonElement transcriptElement : transcripts) {
            JsonObject transcript = transcriptElement.getAsJsonObject();
            JsonArray sentences = transcript.getAsJsonArray("sentences");

            for (JsonElement sentenceElement : sentences) {
                JsonObject sentence = sentenceElement.getAsJsonObject();
                String text = sentence.get("text").getAsString();

                if (keepEmotions) {
                    extractTags(sentence, text, EMOTION_LIST, "emotion");
                }

                if (keepEvents) {
                    extractTags(sentence, text, EVENT_LIST, "event");
                }

                if (keepTrans) {
                    String cleanText = getCleanText(text, tagsToCleanup);
                    sentence.addProperty("text", cleanText);
                } else {
                    sentence.remove("text");
                }
            }

            if (keepTrans) {
                transcript.addProperty("text", getCleanText(transcript.get("text").getAsString(), tagsToCleanup));
            } else {
                transcript.remove("text");
            }

            JsonArray filteredSentences = new JsonArray();
            for (JsonElement sentenceElement : sentences) {
                JsonObject sentence = sentenceElement.getAsJsonObject();
                if (sentence.has("text") || sentence.has("emotion") || sentence.has("event")) {
                    filteredSentences.add(sentence);
                }
            }
            transcript.add("sentences", filteredSentences);
        }
        return data;
    }

    private static void extractTags(JsonObject sentence, String text, List<String> tagList, String key) {
        String pattern = "<\\|(" + String.join("|", tagList) + ")\\|>";
        Pattern compiledPattern = Pattern.compile(pattern);
        Matcher matcher = compiledPattern.matcher(text);
        Set<String> tags = new HashSet<>();

        while (matcher.find()) {
            tags.add(matcher.group(1));
        }

        if (!tags.isEmpty()) {
            JsonArray tagArray = new JsonArray();
            tags.forEach(tagArray::add);
            sentence.add(key, tagArray);
        } else {
            sentence.remove(key);
        }
    }

    private static String getCleanText(String text, List<String> tagsToCleanup) {
        for (String tag : tagsToCleanup) {
            text = text.replace(tag, "");
        }
        return text.replaceAll("\\s{2,}", " ").trim();
    }
}

/**
 * 通义千问ASR情绪分析Demo
 * 使用SenseVoice模型进行语音识别和情绪分析
 */
public class Main {
    
    // API Key - 请替换为您的实际API Key
    private static final String API_KEY = "sk-c2ef44dd6ee14584bad5b8d70203b87f";
    
    // 测试音频文件URL
    private static final String TEST_AUDIO_URL = "https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/sensevoice/rich_text_example_1.wav";

    public static void main(String[] args) {
        System.out.println("=== 通义千问ASR情绪分析Demo ===");
        System.out.println("使用SenseVoice模型进行语音识别和情绪分析");
        System.out.println("测试音频: " + TEST_AUDIO_URL);
        System.out.println();
        
        try {
            // 创建转写请求参数
            TranscriptionParam param = TranscriptionParam.builder()
                    .apiKey(API_KEY)
                    .model("sensevoice-v1")
                    .fileUrls(Arrays.asList(TEST_AUDIO_URL))
                    .parameter("language_hints", new String[] {"en"})
                    .build();

            Transcription transcription = new Transcription();
            
            // 提交转写请求
            System.out.println("正在提交语音识别任务...");
            TranscriptionResult result = transcription.asyncCall(param);
            System.out.println("任务ID: " + result.getTaskId());
            System.out.println("请求ID: " + result.getRequestId());
            
            // 等待转写完成
            System.out.println("等待识别完成...");
            result = transcription.wait(
                    TranscriptionQueryParam.FromTranscriptionParam(param, result.getTaskId()));
            
            // 获取转写结果
            List<TranscriptionTaskResult> taskResultList = result.getResults();
            if (taskResultList != null && taskResultList.size() > 0) {
                System.out.println("识别完成！正在解析结果...");
                System.out.println();
                
                for (TranscriptionTaskResult taskResult : taskResultList) {
                    if ("SUCCEEDED".equals(taskResult.getSubtaskStatus())) {
                        String transcriptionUrl = taskResult.getTranscriptionUrl();
                        HttpURLConnection connection = 
                                (HttpURLConnection) new URL(transcriptionUrl).openConnection();
                        connection.setRequestMethod("GET");
                        connection.connect();
                        
                        BufferedReader reader = 
                                new BufferedReader(new InputStreamReader(connection.getInputStream()));
                        Gson gson = new GsonBuilder().setPrettyPrinting().create();
                        JsonElement jsonResult = gson.fromJson(reader, JsonObject.class);
                        
                        // 解析原始结果
                        System.out.println("=== 原始识别结果 ===");
                        System.out.println(gson.toJson(jsonResult));
                        System.out.println();
                        
                        // 解析情绪和事件
                        JsonObject parsedResult = SenseVoiceParser.parseSenseVoiceResult(
                                jsonResult.getAsJsonObject(), true, true, true);
                        
                        System.out.println("=== 情绪分析结果 ===");
                        displayEmotionAnalysis(parsedResult);
                        
                    } else {
                        System.out.println("识别失败: " + taskResult.getSubtaskStatus());
                        System.out.println("错误信息: " + taskResult);
                    }
                }
            } else {
                System.out.println("未获取到识别结果");
            }
            
        } catch (Exception e) {
            System.out.println("发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 显示情绪分析结果
     */
    private static void displayEmotionAnalysis(JsonObject result) {
        JsonArray transcripts = result.getAsJsonArray("transcripts");
        
        for (JsonElement transcriptElement : transcripts) {
            JsonObject transcript = transcriptElement.getAsJsonObject();
            
            System.out.println("音频文件: " + transcript.get("file_url").getAsString());
            System.out.println("总时长: " + transcript.getAsJsonObject("properties")
                    .get("original_duration_in_milliseconds").getAsInt() + "ms");
            
            if (transcript.has("text")) {
                System.out.println("识别文本: " + transcript.get("text").getAsString());
            }
            System.out.println();
            
            JsonArray sentences = transcript.getAsJsonArray("sentences");
            for (int i = 0; i < sentences.size(); i++) {
                JsonObject sentence = sentences.get(i).getAsJsonObject();
                
                System.out.println("片段 " + (i + 1) + ":");
                System.out.println("  时间: " + sentence.get("begin_time").getAsInt() + 
                        "ms - " + sentence.get("end_time").getAsInt() + "ms");
                
                if (sentence.has("text")) {
                    System.out.println("  文本: " + sentence.get("text").getAsString());
                }
                
                if (sentence.has("emotion")) {
                    JsonArray emotions = sentence.getAsJsonArray("emotion");
                    System.out.print("  情绪: ");
                    for (int j = 0; j < emotions.size(); j++) {
                        String emotion = emotions.get(j).getAsString();
                        String emotionChinese = getEmotionChinese(emotion);
                        System.out.print(emotionChinese + "(" + emotion + ")");
                        if (j < emotions.size() - 1) System.out.print(", ");
                    }
                    System.out.println();
                }
                
                if (sentence.has("event")) {
                    JsonArray events = sentence.getAsJsonArray("event");
                    System.out.print("  事件: ");
                    for (int j = 0; j < events.size(); j++) {
                        String event = events.get(j).getAsString();
                        String eventChinese = getEventChinese(event);
                        System.out.print(eventChinese + "(" + event + ")");
                        if (j < events.size() - 1) System.out.print(", ");
                    }
                    System.out.println();
                }
                System.out.println();
            }
        }
    }
    
    /**
     * 获取情绪的中文描述
     */
    private static String getEmotionChinese(String emotion) {
        switch (emotion) {
            case "NEUTRAL": return "中性";
            case "HAPPY": return "高兴";
            case "ANGRY": return "生气";
            case "SAD": return "伤心";
            default: return emotion;
        }
    }
    
    /**
     * 获取事件的中文描述
     */
    private static String getEventChinese(String event) {
        switch (event) {
            case "Speech": return "说话声";
            case "Applause": return "掌声";
            case "BGM": return "背景音乐";
            case "Laughter": return "笑声";
            default: return event;
        }
    }
}
