@echo off
echo =================================
echo 通义千问ASR情绪分析Demo - 编译脚本
echo =================================

echo 检查Java版本...
java -version
echo.

echo 检查Maven版本...
mvn -version
echo.

echo 开始编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 打包项目...
mvn package
if %errorlevel% neq 0 (
    echo 打包失败！
    pause
    exit /b 1
)

echo.
echo =================================
echo 编译成功！
echo 生成的JAR文件: target\tongyi-asr-demo-1.0.0.jar
echo 可以运行 run.bat 来执行程序
echo =================================
pause
