# 情绪分析Demo



## 功能特性

- 语音识别：将音频文件转换为文本
- 情绪分析：识别四种情绪（中性、高兴、生气、伤心）
- 事件检测：识别四种音频事件（说话声、掌声、背景音乐、笑声）
- 支持多种音频格式

## 项目结构

```
audios/
├── pom.xml              # Maven项目配置
├── src/
│   └── main/
│       └── java/
│           └── Main.java    # 主程序
├── compile.bat          # 编译脚本
├── run.bat             # 运行脚本
└── README.md           # 说明文档
```

## 使用方法

### 1. 编译项目
双击运行 `compile.bat` 或在命令行执行：
```bash
compile.bat
```

### 2. 运行程序
双击运行 `run.bat` 或在命令行执行：
```bash
run.bat
```

## 技术说明

- **JDK版本**: 1.8
- **构建工具**: Maven
- **主要依赖**: DashScope SDK, Gson
- **AI模型**: SenseVoice-v1
- **API Key**: 已配置在代码中

## 输出示例

程序会输出：
1. 原始识别结果（JSON格式）
2. 情绪分析结果（包含时间戳、文本、情绪、事件）

## 注意事项

- 确保网络连接正常，程序需要访问阿里云API
- 首次运行会下载Maven依赖，可能需要较长时间
- 程序使用示例音频文件进行测试
